const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 检查数据库数据...\n');

    // 检查用户
    const users = await prisma.user.findMany();
    console.log(`👥 用户数量: ${users.length}`);
    if (users.length > 0) {
      users.forEach(user => {
        console.log(`   - ${user.username} (${user.email}) - ${user.role}`);
      });
    }
    console.log('');

    // 检查中国项目
    const chinaPrograms = await prisma.chinaProgram.findMany();
    console.log(`🇨🇳 中国项目数量: ${chinaPrograms.length}`);
    if (chinaPrograms.length > 0) {
      chinaPrograms.forEach(program => {
        console.log(`   - ${program.title} (${program.status})`);
      });
    }
    console.log('');

    // 检查国际项目
    const internationalPrograms = await prisma.internationalProgram.findMany();
    console.log(`🌍 国际项目数量: ${internationalPrograms.length}`);
    if (internationalPrograms.length > 0) {
      internationalPrograms.forEach(program => {
        console.log(`   - ${program.title} (${program.status})`);
      });
    }
    console.log('');

    // 检查博客
    const blogs = await prisma.blog.findMany();
    console.log(`📝 博客数量: ${blogs.length}`);
    if (blogs.length > 0) {
      blogs.forEach(blog => {
        console.log(`   - ${blog.title} (${blog.status})`);
      });
    }
    console.log('');

    // 检查学员故事
    const testimonials = await prisma.testimonial.findMany();
    console.log(`💬 学员故事数量: ${testimonials.length}`);
    if (testimonials.length > 0) {
      testimonials.forEach(testimonial => {
        console.log(`   - ${testimonial.name} - ${testimonial.program}`);
      });
    }
    console.log('');

    // 检查城市
    const cities = await prisma.city.findMany();
    console.log(`🏙️ 城市数量: ${cities.length}`);
    if (cities.length > 0 && cities.length <= 10) {
      cities.forEach(city => {
        console.log(`   - ${city.nameZh} / ${city.nameEn}`);
      });
    } else if (cities.length > 10) {
      console.log(`   - 显示前10个城市:`);
      cities.slice(0, 10).forEach(city => {
        console.log(`     - ${city.nameZh} / ${city.nameEn}`);
      });
      console.log(`   - ... 还有 ${cities.length - 10} 个城市`);
    }
    console.log('');

    // 检查国家
    const countries = await prisma.country.findMany();
    console.log(`🌏 国家数量: ${countries.length}`);
    if (countries.length > 0) {
      countries.forEach(country => {
        console.log(`   - ${country.nameZh} / ${country.nameEn}`);
      });
    }
    console.log('');

    // 检查项目类型
    const programTypes = await prisma.programType.findMany();
    console.log(`📋 项目类型数量: ${programTypes.length}`);
    if (programTypes.length > 0) {
      programTypes.forEach(type => {
        console.log(`   - ${type.nameZh} / ${type.nameEn}`);
      });
    }
    console.log('');

    // 检查年级级别
    const gradeLevels = await prisma.gradeLevel.findMany();
    console.log(`🎓 年级级别数量: ${gradeLevels.length}`);
    if (gradeLevels.length > 0) {
      gradeLevels.forEach(level => {
        console.log(`   - ${level.nameZh} / ${level.nameEn}`);
      });
    }
    console.log('');

    // 检查FAQ
    const faqs = await prisma.fAQ.findMany();
    console.log(`❓ FAQ数量: ${faqs.length}`);
    if (faqs.length > 0) {
      faqs.forEach(faq => {
        console.log(`   - ${faq.question.substring(0, 50)}...`);
      });
    }
    console.log('');

    // 检查视频
    const videos = await prisma.video.findMany();
    console.log(`🎥 视频数量: ${videos.length}`);
    if (videos.length > 0) {
      videos.forEach(video => {
        console.log(`   - ${video.title}`);
      });
    }
    console.log('');

    // 检查首页展示
    const homepageShowcases = await prisma.homepageShowcase.findMany();
    console.log(`🏠 首页展示数量: ${homepageShowcases.length}`);
    if (homepageShowcases.length > 0) {
      homepageShowcases.forEach(showcase => {
        console.log(`   - 位置 ${showcase.position}: ${showcase.customTitleZh || showcase.customTitleEn || '无标题'}`);
      });
    }
    console.log('');

    // 检查Hero页面
    const heroPages = await prisma.heroPage.findMany();
    console.log(`🎯 Hero页面数量: ${heroPages.length}`);
    if (heroPages.length > 0) {
      heroPages.forEach(hero => {
        console.log(`   - ${hero.titleZh || hero.titleEn} (${hero.pageType})`);
      });
    }
    console.log('');

    // 检查合作伙伴
    const partners = await prisma.partner.findMany();
    console.log(`🤝 合作伙伴数量: ${partners.length}`);
    if (partners.length > 0) {
      partners.forEach(partner => {
        console.log(`   - ${partner.name}`);
      });
    }

    console.log('\n✅ 数据库检查完成!');

  } catch (error) {
    console.error('❌ 检查数据库时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
