import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'zh'
    const limit = parseInt(searchParams.get('limit') || '6')

    // Get active homepage showcase items
    const showcases = await prisma.homepageShowcase.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { order: 'asc' },
        { createdAt: 'desc' }
      ],
      take: limit,
    })

    // Fetch program details for each showcase item
    const showcasePrograms = await Promise.all(
      showcases.map(async (showcase) => {
        let program = null

        if (showcase.programType === 'china') {
          // Fetch China program
          const chinaProgram = await prisma.chinaProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
            include: {
              city: {
                select: {
                  name: true,
                  nameEn: true,
                },
              },
              translations: true,
            },
          })

          if (chinaProgram) {
            // Get translation if available
            const translation = chinaProgram.translations.find(t => t.language === language)

            program = {
              id: chinaProgram.id,
              title: language === 'zh'
                ? (showcase.titleZh || chinaProgram.title)
                : (showcase.titleEn || translation?.title || chinaProgram.title),
              description: language === 'zh'
                ? chinaProgram.description
                : (translation?.description || chinaProgram.description),
              image: chinaProgram.featuredImage || '/placeholder-program.jpg',
              type: language === 'zh'
                ? (showcase.programTypeZh || 'Study China')
                : (showcase.programTypeEn || 'Study China'),
              city: language === 'zh'
                ? (showcase.cityZh || chinaProgram.city?.name || '')
                : (showcase.cityEn || chinaProgram.city?.nameEn || chinaProgram.city?.name || ''),
              link: `/study-china/${chinaProgram.slug}`,
            }
          }
        } else if (showcase.programType === 'international') {
          // Fetch International program
          const internationalProgram = await prisma.internationalProgram.findFirst({
            where: {
              slug: showcase.programSlug,
              status: 'PUBLISHED',
            },
            include: {
              city: {
                select: {
                  name: true,
                  nameEn: true,
                },
              },
              translations: true,
            },
          })

          if (internationalProgram) {
            // Get translation if available
            const translation = internationalProgram.translations.find(t => t.language === language)

            program = {
              id: internationalProgram.id,
              title: language === 'zh'
                ? (showcase.titleZh || internationalProgram.title)
                : (showcase.titleEn || translation?.title || internationalProgram.title),
              description: language === 'zh'
                ? internationalProgram.description
                : (translation?.description || internationalProgram.description),
              image: internationalProgram.featuredImage || '/placeholder-program.jpg',
              type: language === 'zh'
                ? (showcase.programTypeZh || 'Study International')
                : (showcase.programTypeEn || 'Study International'),
              city: language === 'zh'
                ? (showcase.cityZh || internationalProgram.city?.name || '')
                : (showcase.cityEn || internationalProgram.city?.nameEn || internationalProgram.city?.name || ''),
              link: `/study-international/${internationalProgram.slug}`,
            }
          }
        }

        return {
          position: showcase.position,
          program,
        }
      })
    )

    // Filter out showcases where program was not found and sort by position
    const validShowcases = showcasePrograms
      .filter(item => item.program !== null)
      .sort((a, b) => a.position - b.position)
      .map(item => item.program)

    return NextResponse.json({
      programs: validShowcases,
    })
  } catch (error) {
    console.error('Get homepage showcase error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
